# PrinterManagerController 接口说明文档

## 概述

PrinterManagerController 是打印机管理控制器，提供打印机设备检查、标签模板管理、打印记录管理等功能。

**基础信息：**
- 控制器路径：`api/PrinterManager/[action]`
- 响应格式：`application/json`
- 请求方式：所有接口均为 `POST` 请求

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "Head": {
    "Code": "200",
    "Message": "操作成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    // 具体的响应数据
  }
}
```

**状态码说明：**
- `200`：操作成功
- `1000`：参数错误
- `2001`：打印机设备不存在
- `2002`：文件格式错误
- `999`：系统错误

## 接口列表

### 1. 检查打印机设备是否在库

**接口地址：** `POST /api/PrinterManager/CheckPrinterDevice`

**功能描述：** 根据打印机型号和SN码检查设备是否在库存中，并记录查询统计信息

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "Model": "string",  // 打印机型号（必填，1-100字符）
    "SN": "string"      // 设备SN码（必填，1-100字符）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "查询成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Model": "string",              // 打印机型号
    "SN": "string",                 // 设备SN码
    "IsInStock": true,              // 设备是否在库（true:在库 false:不在库）
    "FirstQueryTime": "2024-01-01T12:00:00", // 首次查询时间
    "LastQueryTime": "2024-01-01T12:00:00",  // 最后查询时间
    "QueryCount": 10,               // 查询次数
    "Remark": "string"              // 备注信息
  }
}
```

### 2. 解析Excel文件生成打印标签内容

**接口地址：** `POST /api/PrinterManager/PrintLabelByExcel`

**功能描述：** 上传Excel文件并按指定的模板长度解析生成标签内容

**请求参数：** `multipart/form-data`
- `file`：Excel文件（必填，支持.xls/.xlsx/.csv格式）
- `templateLength`：模板长度（必填，大于0的整数，表示每个标签包含的字段数量）
- `hasHeader`：是否包含标题栏（可选，默认false，true:跳过第一行 false:不跳过）

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "解析成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "print_labels": [
      {
        "label_contents": ["字段1", "字段2", "字段3"]  // 字符串数组，长度等于templateLength
      }
    ]
  }
}
```

### 3. 保存用户自定义标签模板

**接口地址：** `POST /api/PrinterManager/AddLabel`

**功能描述：** 保存用户自定义的标签模板到数据库

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "Json": "string"     // 标签模板JSON内容（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "保存成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 保存结果（true:成功 false:失败）
}
```

### 4. 获取用户自定义标签模板列表（分页）

**接口地址：** `POST /api/PrinterManager/GetLabels`

**功能描述：** 分页获取指定用户的自定义标签模板列表

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "PageIndex": 0,      // 页码（从0开始）
  "PageSize": 10,      // 页大小（0表示不分页，最大2500）
  "OrderFile": "id",   // 排序字段
  "SortType": 0,       // 排序类型(0:正序 1:倒序)
  "RequestParams": {
    "OpenId": "string" // 微信用户OpenId（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "获取成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": {
    "Total": 100,  // 总记录数
    "Data": [      // 标签模板列表
      {
        // 标签模板详细信息
      }
    ]
  }
}
```

### 5. 删除用户自定义标签模板

**接口地址：** `POST /api/PrinterManager/DeleteLabel`

**功能描述：** 删除指定用户的自定义标签模板

**请求参数：**
```json
{
  "Head": {
    "RequestId": "string",
    "Uid": "string",
    "AppId": 0
  },
  "Body": {
    "OpenId": "string",  // 微信用户OpenId（必填）
    "LabelId": 123       // 标签模板ID（必填）
  }
}
```

**响应数据：**
```json
{
  "Head": {
    "Code": "200",
    "Message": "删除成功",
    "CallTime": "2024-01-01 12:00:00"
  },
  "Result": true  // 删除结果（true:成功 false:失败）
}
```
