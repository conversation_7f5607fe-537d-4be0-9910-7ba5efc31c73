using System.Threading.Tasks;
using Welshine.Official.Domain.VO.App.Response;

namespace Welshine.Official.Service.Interface
{
    /// <summary>
    /// 打印机管理服务接口
    /// </summary>
    public interface IPrinterManagerService
    {
        /// <summary>
        /// 检查打印机设备是否在库
        /// </summary>
        /// <param name="model">打印机型号</param>
        /// <param name="sn">设备SN码</param>
        /// <returns></returns>
        Task<CheckPrinterDeviceResponse> CheckPrinterDevice(string model, string sn);

        /// <summary>
        /// 添加打印记录
        /// </summary>
        /// <param name="openId">微信OpenId（可选）</param>
        /// <param name="printerSN">打印设备SN</param>
        /// <param name="templateId">打印模版id</param>
        /// <param name="printCount">打印份数</param>
        /// <param name="resultCode">打印结果编码</param>
        /// <param name="resultDescription">打印结果描述</param>
        /// <returns></returns>
        Task<bool> AddPrintRecord(string openId, string printerSN, long templateId, int printCount, string resultCode, string resultDescription);
    }
}
