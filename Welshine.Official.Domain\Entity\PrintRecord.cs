using System;

namespace Welshine.Official.Domain.Entity
{
    /// <summary>
    /// 打印记录表
    /// </summary>
    [SqlSugar.SugarTable("ext_print_record")]
    public class PrintRecord : BaseBigEntity
    {
        /// <summary>
        /// 微信 OpenId（可选）
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "open_id", IsNullable = true)]
        public string OpenId { get; set; }

        /// <summary>
        /// 打印设备SN
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "printer_sn")]
        public string PrinterSN { get; set; }

        /// <summary>
        /// 打印模版id
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "template_id")]
        public long TemplateId { get; set; }

        /// <summary>
        /// 打印份数
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "print_count")]
        public int PrintCount { get; set; }

        /// <summary>
        /// 打印结果编码
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "result_code")]
        public string ResultCode { get; set; }

        /// <summary>
        /// 打印结果描述
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "result_description")]
        public string ResultDescription { get; set; }

        /// <summary>
        /// 打印时间
        /// </summary>
        [SqlSugar.SugarColumn(ColumnName = "print_time")]
        public DateTime PrintTime { get; set; }
    }
}
