using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.App.Request;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 打印机管理控制器
    /// 提供打印机设备检查、标签模板管理、打印记录管理等功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]/[action]")]
    [Produces("application/json")]
    public class PrinterManagerController : BaseApiController
    {
        private readonly IPrinterManagerService _printerManagerService;
        private readonly ILabelService _labelService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="printerManagerService">打印机管理服务</param>
        /// <param name="labelService">标签服务</param>
        public PrinterManagerController(IPrinterManagerService printerManagerService, ILabelService labelService)
        {
            _printerManagerService = printerManagerService;
            _labelService = labelService;
        }

        /// <summary>
        /// 检查打印机设备是否在库
        /// </summary>
        /// <param name="request">检查设备请求参数</param>
        /// <returns>设备检查结果，包含设备信息和查询统计</returns>
        /// <remarks>
        /// 根据打印机型号和SN码检查设备是否在库存中，并记录查询统计信息
        ///
        /// 请求参数说明：
        /// - Model: 打印机型号（必填，1-100字符）
        /// - SN: 设备SN码（必填，1-100字符）
        ///
        /// 响应数据说明：
        /// - Model: 打印机型号
        /// - SN: 设备SN码
        /// - IsInStock: 设备是否在库（true:在库 false:不在库）
        /// - FirstQueryTime: 首次查询时间
        /// - LastQueryTime: 最后查询时间
        /// - QueryCount: 查询次数
        /// - Remark: 备注信息
        /// </remarks>
        /// <response code="200">查询成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="2001">打印机设备不存在</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<CheckPrinterDeviceResponse>> CheckPrinterDevice([FromBody] BaseRequest<CheckPrinterDeviceRequest> request)
        {
            CheckPrinterDeviceResponse result = null;
            try
            {
                result = await _printerManagerService.CheckPrinterDevice(request.Body.Model, request.Body.SN);
                return Success("查询成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("CheckPrinterDevice Error {u}", ex.Message);
                return Failure<CheckPrinterDeviceResponse>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 解析Excel文件生成打印标签内容
        /// </summary>
        /// <param name="file">上传的Excel文件（支持.xls/.xlsx/.csv格式）</param>
        /// <param name="templateLength">模板内容长度（每个标签包含的字段数量）</param>
        /// <param name="hasHeader">是否包含标题栏（true:跳过第一行 false:不跳过）</param>
        /// <returns>解析后的标签内容数组</returns>
        /// <remarks>
        /// 上传Excel文件并按指定的模板长度解析生成标签内容
        ///
        /// 请求参数说明：
        /// - file: Excel文件（必填，支持.xls/.xlsx/.csv格式）
        /// - templateLength: 模板长度（必填，大于0的整数，表示每个标签包含的字段数量）
        /// - hasHeader: 是否包含标题栏（可选，默认false）
        ///
        /// 响应数据说明：
        /// - print_labels: 标签内容数组，每个元素包含label_contents字段
        /// - label_contents: 字符串数组，长度等于templateLength
        ///
        /// 处理逻辑：
        /// 1. 读取Excel文件的第一个工作表
        /// 2. 如果hasHeader为true，跳过第一行
        /// 3. 按templateLength长度对每行数据进行切片
        /// 4. 不足长度的用空字符串补齐
        /// </remarks>
        /// <response code="200">解析成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="2002">文件格式错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<object>> PrintLabelByExcel([FromForm] IFormFile file, [FromForm] int templateLength, [FromForm] bool hasHeader)
        {
            if (file == null || file.Length == 0)
            {
                return Failure<object>(ErrorCode.FileFormatError, null);
            }
            if (templateLength <= 0)
            {
                return Failure<object>(ErrorCode.Param, null);
            }
            var allowType = new[] { ".xlsx", ".xls", ".csv" };
            if (!allowType.Any(x => file.FileName.ToLower().EndsWith(x)))
            {
                return Failure<object>(ErrorCode.FileFormatError, null);
            }

            try
            {
                var list = new List<string[]>();
                using (var stream = new MemoryStream())
                {
                    await file.CopyToAsync(stream);
                    stream.Position = 0;
                    list = ExcelImportHelper.ParseFirstSheetRows(stream, file.FileName);
                }

                if (hasHeader && list.Count > 0)
                {
                    list.RemoveAt(0);
                }

                // 按模板长度切片
                var result = new
                {
                    print_labels = list
                        .Where(r => r != null && r.Length > 0)
                        .SelectMany(r =>
                        {
                            var items = new List<Dictionary<string, object>>();
                            for (int i = 0; i < r.Length; i += templateLength)
                            {
                                var slice = r.Skip(i).Take(templateLength).Select(x => x ?? string.Empty).ToArray();
                                if (slice.Length < templateLength)
                                {
                                    var padded = new string[templateLength];
                                    for (int k = 0; k < templateLength; k++)
                                    {
                                        padded[k] = k < slice.Length ? slice[k] : string.Empty;
                                    }
                                    slice = padded;
                                }
                                items.Add(new Dictionary<string, object> { { "label_contents", slice } });
                            }
                            return items;
                        }).ToList()
                };

                return Success<object>("解析成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure<object>(ex.Code, ex.Message, null);
            }
            catch (System.Exception ex)
            {
                Log.Error("PrintLabelByExcel Error {u}", ex.Message);
                return Failure<object>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", null);
            }
        }

        /// <summary>
        /// 保存用户自定义标签模板
        /// </summary>
        /// <param name="request">保存标签模板请求参数</param>
        /// <returns>保存结果</returns>
        /// <remarks>
        /// 保存用户自定义的标签模板到数据库
        ///
        /// 请求参数说明：
        /// - OpenId: 微信用户OpenId（必填）
        /// - Json: 标签模板JSON内容（必填）
        ///
        /// 响应数据说明：
        /// - Result: 保存结果（true:成功 false:失败）
        /// </remarks>
        /// <response code="200">保存成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddLabel([FromBody] BaseRequest<AddLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.AddLabelAsync(request.Body.OpenId, request.Body.Json);
                return Success("保存成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取用户自定义标签模板列表（分页）
        /// </summary>
        /// <param name="request">分页查询请求参数</param>
        /// <returns>用户标签模板分页列表</returns>
        /// <remarks>
        /// 分页获取指定用户的自定义标签模板列表
        ///
        /// 请求参数说明：
        /// - PageIndex: 页码（从0开始）
        /// - PageSize: 页大小（0表示不分页，最大2500）
        /// - RequestParams.OpenId: 微信用户OpenId（必填）
        ///
        /// 响应数据说明：
        /// - Total: 总记录数
        /// - Data: 标签模板列表，包含模板的详细信息
        /// </remarks>
        /// <response code="200">获取成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<PageRows<Domain.Entity.LabelTemplate>>> GetLabels([FromBody] RequestPageModel<GetLabelsRequest> request)
        {
            PageRows<Domain.Entity.LabelTemplate> pageRows = null;
            try
            {
                pageRows = await _labelService.GetLabelsAsync(request.RequestParams.OpenId, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetLabels Error {u}", ex.Message);
                return Failure<PageRows<Domain.Entity.LabelTemplate>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 删除用户自定义标签模板
        /// </summary>
        /// <param name="request">删除标签模板请求参数</param>
        /// <returns>删除结果</returns>
        /// <remarks>
        /// 删除指定用户的自定义标签模板
        ///
        /// 请求参数说明：
        /// - OpenId: 微信用户OpenId（必填）
        /// - LabelId: 标签模板ID（必填）
        ///
        /// 响应数据说明：
        /// - Result: 删除结果（true:成功 false:失败）
        /// </remarks>
        /// <response code="200">删除成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeleteLabel([FromBody] BaseRequest<DeleteLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.DeleteLabelAsync(request.Body.OpenId, request.Body.LabelId);
                return Success("删除成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeleteLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 添加已打印的标签记录
        /// </summary>
        /// <param name="request">添加已打印标签请求参数</param>
        /// <returns>添加结果</returns>
        /// <remarks>
        /// 保存用户已打印的标签记录到数据库，用于历史记录管理
        ///
        /// 请求参数说明：
        /// - OpenId: 微信用户OpenId（必填）
        /// - Json: 已打印标签的JSON内容（必填）
        ///
        /// 响应数据说明：
        /// - Result: 保存结果（true:成功 false:失败）
        /// </remarks>
        /// <response code="200">保存成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddPrintedLabel([FromBody] BaseRequest<AddPrintedLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.AddPrintedLabelAsync(request.Body.OpenId, request.Body.Json);
                return Success("保存成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddPrintedLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取用户已打印标签记录列表（分页）
        /// </summary>
        /// <param name="request">分页查询请求参数</param>
        /// <returns>已打印标签记录分页列表</returns>
        /// <remarks>
        /// 分页获取指定用户的已打印标签记录列表
        ///
        /// 请求参数说明：
        /// - PageIndex: 页码（从0开始）
        /// - PageSize: 页大小（0表示不分页，最大2500）
        /// - RequestParams.OpenId: 微信用户OpenId（必填）
        ///
        /// 响应数据说明：
        /// - Total: 总记录数
        /// - Data: 已打印标签记录列表，包含打印历史的详细信息
        /// </remarks>
        /// <response code="200">获取成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<PageRows<Domain.Entity.PrintedLabel>>> GetPrintedLabels([FromBody] RequestPageModel<GetPrintedLabelsRequest> request)
        {
            PageRows<Domain.Entity.PrintedLabel> pageRows = null;
            try
            {
                pageRows = await _labelService.GetPrintedLabelsAsync(request.RequestParams.OpenId, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetPrintedLabels Error {u}", ex.Message);
                return Failure<PageRows<Domain.Entity.PrintedLabel>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 删除用户已打印标签记录
        /// </summary>
        /// <param name="request">删除已打印标签请求参数</param>
        /// <returns>删除结果</returns>
        /// <remarks>
        /// 删除指定用户的已打印标签记录
        ///
        /// 请求参数说明：
        /// - OpenId: 微信用户OpenId（必填）
        /// - LabelId: 已打印标签记录ID（必填）
        ///
        /// 响应数据说明：
        /// - Result: 删除结果（true:成功 false:失败）
        /// </remarks>
        /// <response code="200">删除成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<bool>> DeletePrintedLabel([FromBody] BaseRequest<DeletePrintedLabelRequest> request)
        {
            bool ok = false;
            try
            {
                ok = await _labelService.DeletePrintedLabelAsync(request.Body.OpenId, request.Body.LabelId);
                return Success("删除成功", ok);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, ok);
            }
            catch (System.Exception ex)
            {
                Log.Error("DeletePrintedLabel Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", ok);
            }
        }

        /// <summary>
        /// 获取系统标签模板分类列表
        /// </summary>
        /// <param name="request">获取系统标签模板分类请求参数</param>
        /// <returns>系统标签模板分类列表</returns>
        /// <remarks>
        /// 获取所有可用的系统标签模板分类信息
        ///
        /// 请求参数说明：
        /// - 无需特殊参数，获取所有分类
        ///
        /// 响应数据说明：
        /// - Result: 分类列表数组
        /// - CategoryId: 分类ID
        /// - CategoryName: 分类名称
        /// </remarks>
        /// <response code="200">获取成功</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<List<LabelTemplateCategoryResponse>>> GetSystemLabelTemplateCategories([FromBody] BaseRequest<GetSystemLabelTemplateCategoriesRequest> request)
        {
            List<LabelTemplateCategoryResponse> result = null;
            try
            {
                result = await _labelService.GetSystemLabelTemplateCategoriesAsync();
                return Success("获取成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSystemLabelTemplateCategories Error {u}", ex.Message);
                return Failure<List<LabelTemplateCategoryResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }

        /// <summary>
        /// 通过分类获取系统标签模板列表（分页）
        /// </summary>
        /// <param name="request">分页查询请求参数</param>
        /// <returns>系统标签模板分页列表</returns>
        /// <remarks>
        /// 根据分类名称分页获取系统标签模板列表
        ///
        /// 请求参数说明：
        /// - PageIndex: 页码（从0开始）
        /// - PageSize: 页大小（0表示不分页，最大2500）
        /// - RequestParams.CategoryName: 分类名称（可选，为空则获取全部系统模板）
        ///
        /// 响应数据说明：
        /// - Total: 总记录数
        /// - Data: 系统标签模板列表
        /// - TemplateId: 模板ID
        /// - TemplateName: 模板名称
        /// - TemplateContent: 模板内容（JSON格式）
        /// - TemplateThumbnail: 模板缩略图（Base64）
        /// - TemplateDescription: 模板描述
        /// - LabelSize: 标签尺寸
        /// - SceneTags: 适用场景标签
        /// </remarks>
        /// <response code="200">获取成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<PageRows<SystemLabelTemplateResponse>>> GetSystemLabelTemplatesByCategory([FromBody] RequestPageModel<GetSystemLabelTemplatesByCategoryRequest> request)
        {
            PageRows<SystemLabelTemplateResponse> pageRows = null;
            try
            {
                pageRows = await _labelService.GetSystemLabelTemplatesByCategoryAsync(request.RequestParams.CategoryName, request.PageIndex, request.PageSize);
                return Success("获取成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetSystemLabelTemplatesByCategory Error {u}", ex.Message);
                return Failure<PageRows<SystemLabelTemplateResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 搜索系统标签模板列表（分页）
        /// </summary>
        /// <param name="request">搜索请求参数</param>
        /// <returns>搜索结果分页列表</returns>
        /// <remarks>
        /// 根据多个条件搜索系统标签模板
        ///
        /// 请求参数说明：
        /// - PageIndex: 页码（从0开始）
        /// - PageSize: 页大小（0表示不分页，最大2500）
        /// - RequestParams.TemplateName: 模板名称（可选，模糊搜索）
        /// - RequestParams.LabelSize: 标签尺寸（可选，精确匹配）
        /// - RequestParams.SceneTags: 适用场景标签（可选，模糊搜索，支持多个关键词用逗号分隔）
        /// - RequestParams.CategoryName: 分类名称（可选，精确匹配）
        ///
        /// 响应数据说明：
        /// - Total: 总记录数
        /// - Data: 系统标签模板列表
        /// - TemplateId: 模板ID
        /// - TemplateName: 模板名称
        /// - TemplateContent: 模板内容（JSON格式）
        /// - TemplateThumbnail: 模板缩略图（Base64）
        /// - TemplateDescription: 模板描述
        /// - LabelSize: 标签尺寸
        /// - SceneTags: 适用场景标签
        /// </remarks>
        /// <response code="200">搜索成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<PageRows<SystemLabelTemplateResponse>>> SearchSystemLabelTemplates([FromBody] RequestPageModel<SearchSystemLabelTemplatesRequest> request)
        {
            PageRows<SystemLabelTemplateResponse> pageRows = null;
            try
            {
                pageRows = await _labelService.SearchSystemLabelTemplatesAsync(
                    request.RequestParams.TemplateName,
                    request.RequestParams.LabelSize,
                    request.RequestParams.SceneTags,
                    request.RequestParams.CategoryName,
                    request.PageIndex,
                    request.PageSize);
                return Success("搜索成功", pageRows);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, pageRows);
            }
            catch (System.Exception ex)
            {
                Log.Error("SearchSystemLabelTemplates Error {u}", ex.Message);
                return Failure<PageRows<SystemLabelTemplateResponse>>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", pageRows);
            }
        }

        /// <summary>
        /// 添加打印记录
        /// </summary>
        /// <param name="request">添加打印记录请求参数</param>
        /// <returns>添加结果</returns>
        /// <remarks>
        /// 记录打印机的打印操作，包括打印结果和统计信息
        ///
        /// 请求参数说明：
        /// - OpenId: 微信用户OpenId（可选）
        /// - PrinterSN: 打印设备SN（必填，1-100字符）
        /// - TemplateId: 打印模板ID（必填）
        /// - PrintCount: 打印份数（必填，大于0的整数）
        /// - ResultCode: 打印结果编码（必填）
        /// - ResultDescription: 打印结果描述（必填，最大500字符）
        ///
        /// 响应数据说明：
        /// - Result: 添加结果（true:成功 false:失败）
        /// </remarks>
        /// <response code="200">添加打印记录成功</response>
        /// <response code="1000">参数错误</response>
        /// <response code="999">系统错误</response>
        [HttpPost]
        public async Task<BaseResponse<bool>> AddPrintRecord([FromBody] BaseRequest<AddPrintRecordRequest> request)
        {
            bool result = false;
            try
            {
                result = await _printerManagerService.AddPrintRecord(
                    request.Body.OpenId,
                    request.Body.PrinterSN,
                    request.Body.TemplateId,
                    request.Body.PrintCount,
                    request.Body.ResultCode,
                    request.Body.ResultDescription);
                return Success("添加打印记录成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("AddPrintRecord Error {u}", ex.Message);
                return Failure<bool>(ErrorCode.SystemError.GetHashCode(), $"{ex.Message}", result);
            }
        }
    }
}
